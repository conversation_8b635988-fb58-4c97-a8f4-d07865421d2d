import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like, ILike } from 'typeorm';
import { Work, WorkType, WorkStatus } from '../../entities';
import { CreateWorkDto } from './dto/create-work.dto';
import { UpdateWorkDto } from './dto/update-work.dto';
import { PaginationDto, PaginationResponseDto } from '../../common/dto/pagination.dto';

@Injectable()
export class WorksService {
  constructor(
    @InjectRepository(Work)
    private readonly workRepository: Repository<Work>,
  ) {}

  async create(createWorkDto: CreateWorkDto): Promise<Work> {
    // Verificar se já existe uma obra com o mesmo título e tipo
    const existingWork = await this.workRepository.findOne({
      where: {
        title: createWorkDto.title,
        type: createWorkDto.type,
      },
    });

    if (existingWork) {
      throw new ConflictException('Já existe uma obra com este título e tipo');
    }

    const work = this.workRepository.create(createWorkDto);
    return this.workRepository.save(work);
  }

  async findAll(
    paginationDto: PaginationDto,
    filters?: {
      search?: string;
      type?: WorkType;
      status?: WorkStatus;
      genre?: string;
      author?: string;
    },
  ): Promise<PaginationResponseDto<Work>> {
    const { page = 1, limit = 10 } = paginationDto;
    const skip = (page - 1) * limit;

    const queryBuilder = this.workRepository.createQueryBuilder('work');

    // Aplicar filtros
    if (filters?.search) {
      queryBuilder.andWhere(
        '(work.title ILIKE :search OR work.alternativeTitle ILIKE :search OR work.author ILIKE :search)',
        { search: `%${filters.search}%` },
      );
    }

    if (filters?.type) {
      queryBuilder.andWhere('work.type = :type', { type: filters.type });
    }

    if (filters?.status) {
      queryBuilder.andWhere('work.status = :status', { status: filters.status });
    }

    if (filters?.genre) {
      queryBuilder.andWhere(':genre = ANY(work.genres)', { genre: filters.genre });
    }

    if (filters?.author) {
      queryBuilder.andWhere('work.author ILIKE :author', { author: `%${filters.author}%` });
    }

    // Ordenação padrão por nota média e total de avaliações
    queryBuilder.orderBy('work.averageRating', 'DESC');
    queryBuilder.addOrderBy('work.totalRatings', 'DESC');
    queryBuilder.addOrderBy('work.createdAt', 'DESC');

    // Paginação
    queryBuilder.skip(skip).take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      meta: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }

  async findOne(id: number): Promise<Work> {
    const work = await this.workRepository.findOne({
      where: { id },
      relations: ['userWorks', 'notes'],
    });

    if (!work) {
      throw new NotFoundException('Obra não encontrada');
    }

    return work;
  }

  async update(id: number, updateWorkDto: UpdateWorkDto): Promise<Work> {
    const work = await this.findOne(id);

    // Se estiver atualizando título ou tipo, verificar conflitos
    if (updateWorkDto.title || updateWorkDto.type) {
      const title = updateWorkDto.title || work.title;
      const type = updateWorkDto.type || work.type;

      const existingWork = await this.workRepository.findOne({
        where: { title, type },
      });

      if (existingWork && existingWork.id !== id) {
        throw new ConflictException('Já existe uma obra com este título e tipo');
      }
    }

    Object.assign(work, updateWorkDto);
    return this.workRepository.save(work);
  }

  async remove(id: number): Promise<void> {
    const work = await this.findOne(id);
    await this.workRepository.remove(work);
  }

  async updateRating(workId: number): Promise<void> {
    const result = await this.workRepository
      .createQueryBuilder('work')
      .leftJoin('work.userWorks', 'userWork')
      .select('AVG(userWork.rating)', 'averageRating')
      .addSelect('COUNT(userWork.rating)', 'totalRatings')
      .where('work.id = :workId', { workId })
      .andWhere('userWork.rating IS NOT NULL')
      .getRawOne();

    await this.workRepository.update(workId, {
      averageRating: parseFloat(result.averageRating) || 0,
      totalRatings: parseInt(result.totalRatings) || 0,
    });
  }

  async getPopular(limit: number = 10): Promise<Work[]> {
    return this.workRepository.find({
      order: {
        averageRating: 'DESC',
        totalRatings: 'DESC',
      },
      take: limit,
    });
  }

  async getByGenre(genre: string, limit: number = 10): Promise<Work[]> {
    return this.workRepository
      .createQueryBuilder('work')
      .where(':genre = ANY(work.genres)', { genre })
      .orderBy('work.averageRating', 'DESC')
      .limit(limit)
      .getMany();
  }
}
