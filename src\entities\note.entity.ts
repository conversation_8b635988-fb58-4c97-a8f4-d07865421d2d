import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, Index } from "typeorm";
import { ApiProperty } from "@nestjs/swagger";
import { User } from "./user.entity";
import { Work } from "./work.entity";
import { UserWork } from "./user-work.entity";

@Entity("notes")
@Index(["userId", "workId"])
export class Note {
	@ApiProperty({
		description: "ID único da anotação",
		example: 1,
	})
	@PrimaryGeneratedColumn()
	id: number;

	@ApiProperty({
		description: "ID do usuário",
		example: 1,
	})
	@Column()
	userId: number;

	@ApiProperty({
		description: "ID da obra",
		example: 1,
	})
	@Column()
	workId: number;

	@ApiProperty({
		description: "ID do relacionamento usuário-obra",
		example: 1,
	})
	@Column({ nullable: true })
	userWorkId?: number;

	@ApiProperty({
		description: "Título da anotação",
		example: "Reflexões sobre o Arco de Marineford",
	})
	@Column()
	title: string;

	@ApiProperty({
		description: "Conteúdo da anotação",
		example: "Este arco foi emocionalmente devastador...",
	})
	@Column({ type: "text" })
	content: string;

	@ApiProperty({
		description: "Capítulo relacionado à anotação",
		example: 574,
	})
	@Column({ nullable: true })
	chapter?: number;

	@ApiProperty({
		description: "Se a anotação contém spoilers",
		example: true,
	})
	@Column({ default: false })
	containsSpoilers: boolean;

	@ApiProperty({
		description: "Se a anotação é pública",
		example: false,
	})
	@Column({ default: false })
	isPublic: boolean;

	@ApiProperty({
		description: "Tags da anotação",
		example: ["teoria", "emocional", "importante"],
		type: [String],
	})
	@Column("simple-array", { nullable: true })
	tags?: string[];

	@ApiProperty({
		description: "Data de criação",
	})
	@CreateDateColumn()
	createdAt: Date;

	@ApiProperty({
		description: "Data de última atualização",
	})
	@UpdateDateColumn()
	updatedAt: Date;

	// Relacionamentos
	@ManyToOne(() => User, user => user.notes, { onDelete: "CASCADE" })
	@JoinColumn({ name: "userId" })
	user: User;

	@ManyToOne(() => Work, work => work.notes, { onDelete: "CASCADE" })
	@JoinColumn({ name: "workId" })
	work: Work;

	@ManyToOne(() => UserWork, userWork => userWork.notes, { onDelete: "CASCADE" })
	@JoinColumn({ name: "userWorkId" })
	userWork?: UserWork;
}
