version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: manga-tracker-db
    restart: always
    environment:
      POSTGRES_DB: manga_tracker
      POSTGRES_USER: manga_user
      POSTGRES_PASSWORD: manga_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql

  pgadmin:
    image: dpage/pgadmin4
    container_name: manga-tracker-pgadmin
    restart: always
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
    ports:
      - "8080:80"
    depends_on:
      - postgres

volumes:
  postgres_data:
