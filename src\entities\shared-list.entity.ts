import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn, OneToMany } from "typeorm";
import { ApiProperty } from "@nestjs/swagger";
import { User } from "./user.entity";
import { SharedListItem } from "./shared-list-item.entity";

@Entity("shared_lists")
export class SharedList {
	@ApiProperty({
		description: "ID único da lista compartilhada",
		example: 1,
	})
	@PrimaryGeneratedColumn()
	id: number;

	@ApiProperty({
		description: "ID do proprietário",
		example: 1,
	})
	@Column()
	ownerId: number;

	@ApiProperty({
		description: "Nome da lista",
		example: "Recomendações de Ação",
	})
	@Column()
	name: string;

	@ApiProperty({
		description: "Descrição da lista",
		example: "Mangás de ação que todo mundo deveria ler",
	})
	@Column({ type: "text", nullable: true })
	description?: string;

	@ApiProperty({
		description: "Código único para compartilhamento",
		example: "abc123def456",
	})
	@Column({ unique: true })
	shareCode: string;

	@ApiProperty({
		description: "Se a lista está ativa",
		example: true,
	})
	@Column({ default: true })
	isActive: boolean;

	@ApiProperty({
		description: "Número de visualizações",
		example: 150,
	})
	@Column({ default: 0 })
	viewCount: number;

	@ApiProperty({
		description: "Data de expiração (opcional)",
	})
	@Column({ nullable: true })
	expiresAt?: Date;

	@ApiProperty({
		description: "Data de criação",
	})
	@CreateDateColumn()
	createdAt: Date;

	@ApiProperty({
		description: "Data de última atualização",
	})
	@UpdateDateColumn()
	updatedAt: Date;

	// Relacionamentos
	@ManyToOne(() => User, user => user.sharedLists, { onDelete: "CASCADE" })
	@JoinColumn({ name: "ownerId" })
	owner: User;

	@OneToMany(() => SharedListItem, sharedListItem => sharedListItem.sharedList, {
		cascade: true,
	})
	items: SharedListItem[];
}
