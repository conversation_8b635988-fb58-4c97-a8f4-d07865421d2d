import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { WorksService } from './works.service';
import { CreateWorkDto } from './dto/create-work.dto';
import { UpdateWorkDto } from './dto/update-work.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { PaginationDto } from '../../common/dto/pagination.dto';
import { Work, WorkType, WorkStatus } from '../../entities';

@ApiTags('Obras')
@Controller('works')
export class WorksController {
  constructor(private readonly worksService: WorksService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Criar nova obra',
    description: 'Adiciona uma nova obra (mangá, manhwa ou manhua) ao sistema',
  })
  @ApiResponse({
    status: 201,
    description: 'Obra criada com sucesso',
    type: Work,
  })
  @ApiResponse({
    status: 409,
    description: 'Já existe uma obra com este título e tipo',
  })
  @ApiResponse({
    status: 401,
    description: 'Token inválido ou expirado',
  })
  create(@Body() createWorkDto: CreateWorkDto) {
    return this.worksService.create(createWorkDto);
  }

  @Get()
  @ApiOperation({
    summary: 'Listar obras',
    description: 'Retorna uma lista paginada de obras com filtros opcionais',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Número da página' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Itens por página' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Buscar por título, autor ou título alternativo' })
  @ApiQuery({ name: 'type', required: false, enum: WorkType, description: 'Filtrar por tipo' })
  @ApiQuery({ name: 'status', required: false, enum: WorkStatus, description: 'Filtrar por status' })
  @ApiQuery({ name: 'genre', required: false, type: String, description: 'Filtrar por gênero' })
  @ApiQuery({ name: 'author', required: false, type: String, description: 'Filtrar por autor' })
  @ApiResponse({
    status: 200,
    description: 'Lista de obras retornada com sucesso',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/Work' },
        },
        meta: {
          type: 'object',
          properties: {
            page: { type: 'number' },
            limit: { type: 'number' },
            total: { type: 'number' },
            totalPages: { type: 'number' },
            hasNext: { type: 'boolean' },
            hasPrev: { type: 'boolean' },
          },
        },
      },
    },
  })
  findAll(
    @Query() paginationDto: PaginationDto,
    @Query('search') search?: string,
    @Query('type') type?: WorkType,
    @Query('status') status?: WorkStatus,
    @Query('genre') genre?: string,
    @Query('author') author?: string,
  ) {
    const filters = { search, type, status, genre, author };
    return this.worksService.findAll(paginationDto, filters);
  }

  @Get('popular')
  @ApiOperation({
    summary: 'Obras populares',
    description: 'Retorna as obras mais bem avaliadas',
  })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Número de obras a retornar' })
  @ApiResponse({
    status: 200,
    description: 'Obras populares retornadas com sucesso',
    type: [Work],
  })
  getPopular(@Query('limit', ParseIntPipe) limit: number = 10) {
    return this.worksService.getPopular(limit);
  }

  @Get('genre/:genre')
  @ApiOperation({
    summary: 'Obras por gênero',
    description: 'Retorna obras de um gênero específico',
  })
  @ApiParam({ name: 'genre', type: String, description: 'Nome do gênero' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Número de obras a retornar' })
  @ApiResponse({
    status: 200,
    description: 'Obras do gênero retornadas com sucesso',
    type: [Work],
  })
  getByGenre(
    @Param('genre') genre: string,
    @Query('limit', ParseIntPipe) limit: number = 10,
  ) {
    return this.worksService.getByGenre(genre, limit);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Obter obra por ID',
    description: 'Retorna os detalhes de uma obra específica',
  })
  @ApiParam({ name: 'id', type: Number, description: 'ID da obra' })
  @ApiResponse({
    status: 200,
    description: 'Obra encontrada com sucesso',
    type: Work,
  })
  @ApiResponse({
    status: 404,
    description: 'Obra não encontrada',
  })
  findOne(@Param('id', ParseIntPipe) id: number) {
    return this.worksService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Atualizar obra',
    description: 'Atualiza os dados de uma obra existente',
  })
  @ApiParam({ name: 'id', type: Number, description: 'ID da obra' })
  @ApiResponse({
    status: 200,
    description: 'Obra atualizada com sucesso',
    type: Work,
  })
  @ApiResponse({
    status: 404,
    description: 'Obra não encontrada',
  })
  @ApiResponse({
    status: 409,
    description: 'Já existe uma obra com este título e tipo',
  })
  @ApiResponse({
    status: 401,
    description: 'Token inválido ou expirado',
  })
  update(@Param('id', ParseIntPipe) id: number, @Body() updateWorkDto: UpdateWorkDto) {
    return this.worksService.update(id, updateWorkDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Remover obra',
    description: 'Remove uma obra do sistema',
  })
  @ApiParam({ name: 'id', type: Number, description: 'ID da obra' })
  @ApiResponse({
    status: 200,
    description: 'Obra removida com sucesso',
  })
  @ApiResponse({
    status: 404,
    description: 'Obra não encontrada',
  })
  @ApiResponse({
    status: 401,
    description: 'Token inválido ou expirado',
  })
  remove(@Param('id', ParseIntPipe) id: number) {
    return this.worksService.remove(id);
  }
}
